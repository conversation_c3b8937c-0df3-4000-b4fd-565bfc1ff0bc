import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Toggle } from '@/components/ui/toggle';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import './editor-sidebar.css'; // Import custom styling for editor/sidebar interaction
import { 
  Save, 
  Download, 
  Upload,
  Share, 
  Clock, 
  History, 
  FileText, 
  FolderOpen,
  Plus,
  Settings,
  Bot,
  Loader2,
  CheckCircle,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff,
  Import,
  Sparkles,
  BookOpen,
  Brain,
  FileCheck,
  Type,
  RefreshCw,
  Minimize2,
  AlignLeft,
  BarChart3,
  Repeat,
  AlertTriangle,
  Edit,
  Send,
  X,
  Copy,
  Bold,
  Wrench,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Code,
  MoreHorizontal,
  Palette,
  Link2,
  Image as ImageIcon,
  Table,
  Highlighter,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Target,
  Search,
  FlaskConical,
  Lightbulb,
  PenTool,
  Filter,
  Layers,
  Zap,
  TrendingUp,
  Wand2
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

import RichTextEditor, { RichTextEditorRef } from './paper-generator/RichTextEditor';
import { DocumentManager } from './DocumentManager';
import { DocumentHistory } from './DocumentHistory';

import { FloatingAIToolbar } from './FloatingAIToolbar';
import { documentService } from '@/services/documentService';
import { documentImportService } from '@/services/documentImport';
import { documentExportService } from './paper-generator/html-export.service';
import { Database } from '@/lib/database.types';
import { editorService } from './paper-generator/editor.service';
import { useAuth } from '@/contexts/AuthContext';
import { UnifiedAIAssistant } from './UnifiedAIAssistant';
import { IntelligentWritingAssistant } from './IntelligentWritingAssistant';
import { AcademicCitationSidebar } from './AcademicCitationSidebar';
import { FloatingToolsPanel } from './FloatingToolsPanel';
import './animations.css';
import './academic-interface.css';
import paperAIService from './paper-generator/paper-ai.service';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';
import { useChangeTracking, ChangeTrackingControls, SectionBasedDiffViewer } from './change-tracking';

type Document = Database['public']['Tables']['user_documents']['Row'];

interface DocumentVersion {
  id: string;
  timestamp: Date;
  title: string;
  content: string;
  wordCount: number;
  description: string;
}

interface EnhancedMainEditorProps {
  initialTitle?: string;
  initialContent?: string;
  initialDocumentId?: string;
}

export function EnhancedMainEditor({ initialTitle, initialContent, initialDocumentId }: EnhancedMainEditorProps) {
  const { user } = useAuth();
  const editorRef = useRef<RichTextEditorRef>(null);

  // Change tracking
  const changeTracking = useChangeTracking();
  
  // Document state
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [title, setTitle] = useState(initialTitle || '');
  const [content, setContent] = useState(initialContent || '');
  const [isDocumentManagerOpen, setIsDocumentManagerOpen] = useState(false);
  
  // Auto-save and status
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error' | 'unsaved'>('saved');
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  
  // Version history
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  
  // UI state
  const [activeView, setActiveView] = useState<'editor' | 'documents'>('editor');
  const [showDocumentHistory, setShowDocumentHistory] = useState(false);
  
  // Import functionality
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Enhanced AI Features State
  const [showAIPanel, setShowAIPanel] = useState(false); // Start with AI panel hidden
  const [selectedText, setSelectedText] = useState('');
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLoadingMessage, setAiLoadingMessage] = useState('');
  const [aiProgress, setAiProgress] = useState(0);
  const [currentAIModel, setCurrentAIModel] = useState('google/gemini-2.5-flash-lite');
  const [showFormattingToolbar, setShowFormattingToolbar] = useState(true);
  const [showUnifiedAI, setShowUnifiedAI] = useState(false);
  const [showFloatingTools, setShowFloatingTools] = useState(false);
  const [toolsPosition, setToolsPosition] = useState({ x: 400, y: 300 });
  
  // Improved AI response handling
  const [aiResponse, setAiResponse] = useState('');
  const [showAIResponse, setShowAIResponse] = useState(false);
  const [aiResponsePosition, setAiResponsePosition] = useState({ x: 0, y: 0 });
  const [currentAIAction, setCurrentAIAction] = useState<'replace' | 'insert' | 'display'>('display');
  const [currentPrompt, setCurrentPrompt] = useState('');

  // Chat functionality
  const [showAIChat, setShowAIChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<Array<{id: string, type: 'user' | 'assistant', content: string, timestamp: Date}>>([]);
  const [currentInput, setCurrentInput] = useState('');

  // Intelligent Writing Assistant functionality
  const [intelligentWritingEnabled, setIntelligentWritingEnabled] = useState(true);
  const [cursorPosition, setCursorPosition] = useState(0);

  // Citation sidebar functionality
  const [showCitationSidebar, setShowCitationSidebar] = useState(false);

  // Selection state
  const [selectionRange, setSelectionRange] = useState<{from: number, to: number} | null>(null);
  
  // Formatting state
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  
  // Font and styling
  const [fontSize, setFontSize] = useState('text-base');
  const [fontFamily, setFontFamily] = useState('font-serif');

  // Table functionality
  const [showTableDialog, setShowTableDialog] = useState(false);
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);
  const [isInTable, setIsInTable] = useState(false);
  const [tableControls, setTableControls] = useState({
    canAddColumn: false,
    canAddRow: false,
    canDeleteColumn: false,
    canDeleteRow: false,
    canMergeCells: false,
    canSplitCell: false
  });

  // Image functionality
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const [showImageControls, setShowImageControls] = useState(false);
  const [imageWidth, setImageWidth] = useState<number>(400);
  const [imageAlignment, setImageAlignmentState] = useState<'left' | 'center' | 'right' | null>(null);

  // Floating AI Toolbar state
  const [showFloatingAI, setShowFloatingAI] = useState(false);
  const [floatingAIPosition, setFloatingAIPosition] = useState({ x: 0, y: 0 });
  const [isFloatingAIInteracting, setIsFloatingAIInteracting] = useState(false);

  // Enhanced Academic AI Options with Research-Specific Features
  const academicAIOptions = [
    // Core Writing Tools
    {
      id: "improve-clarity",
      name: "Improve Clarity",
      description: "Edit to be clearer and more concise, using formal academic language.",
      prompt: "Edit the following text to be clearer and more concise, using formal academic language. Maintain all key information:",
      icon: <Sparkles className="h-4 w-4" />,
      color: "blue",
      category: "writing"
    },
    {
      id: "expand-point",
      name: "Expand Point",
      description: "Expand with relevant evidence, examples, or deeper analysis.",
      prompt: "Expand the following point with relevant evidence, examples, or deeper analysis. Add scholarly depth:",
      icon: <AlignLeft className="h-4 w-4" />,
      color: "indigo",
      category: "writing"
    },
    {
      id: "check-grammar",
      name: "Grammar & Style",
      description: "Fix grammar, spelling, punctuation, and improve academic style.",
      prompt: "Check and fix grammar, spelling, punctuation errors and improve academic writing style in the following text:",
      icon: <CheckCircle className="h-4 w-4" />,
      color: "green",
      category: "writing"
    },
    {
      id: "rephrase-academic",
      name: "Academic Tone",
      description: "Rewrite in formal, objective academic style for peer-reviewed journals.",
      prompt: "Rewrite the following text in a formal, objective academic style suitable for a peer-reviewed journal:",
      icon: <FileText className="h-4 w-4" />,
      color: "emerald",
      category: "writing"
    },
    
    // Research-Specific Tools
    {
      id: "suggest-citations",
      name: "Suggest Citations",
      description: "Find relevant academic sources and format citations (APA style).",
      prompt: "For the following text, suggest 5-7 recent, high-quality academic sources that support these claims. Format in APA style:",
      icon: <BookOpen className="h-4 w-4" />,
      color: "amber",
      category: "research"
    },
    {
      id: "find-gaps",
      name: "Research Gaps",
      description: "Identify research gaps and suggest future directions.",
      prompt: "Analyze the following text and identify potential research gaps, limitations, or areas for future investigation:",
      icon: <Brain className="h-4 w-4" />,
      color: "purple",
      category: "research"
    },
    {
      id: "methodology-suggest",
      name: "Method Suggestions",
      description: "Suggest research methods and experimental approaches.",
      prompt: "Based on the following research question/hypothesis, suggest appropriate research methodologies and experimental approaches:",
      icon: <FlaskConical className="h-4 w-4" />,
      color: "pink",
      category: "research"
    },
    {
      id: "generate-hypotheses",
      name: "Generate Hypotheses",
      description: "Create testable hypotheses based on the research context.",
      prompt: "Based on the following research context, generate 3-5 testable hypotheses with clear rationale:",
      icon: <Lightbulb className="h-4 w-4" />,
      color: "yellow",
      category: "research"
    },
    
    // Analysis Tools
    {
      id: "critique-argument",
      name: "Critical Analysis",
      description: "Analyze strengths, weaknesses, and logical structure.",
      prompt: "Provide a critical analysis of the following argument, evaluating its strengths, weaknesses, logic, and evidence:",
      icon: <Edit className="h-4 w-4" />,
      color: "rose",
      category: "analysis"
    },
    {
      id: "summarize",
      name: "Academic Summary",
      description: "Create concise summary focusing on key findings and implications.",
      prompt: "Summarize the following text in 3-4 sentences, focusing on key findings, methods, and implications:",
      icon: <List className="h-4 w-4" />,
      color: "teal",
      category: "analysis"
    },
    {
      id: "paraphrase",
      name: "Paraphrase",
      description: "Rewrite to avoid plagiarism while preserving meaning.",
      prompt: "Paraphrase the following text to avoid plagiarism while preserving all key information and academic meaning:",
      icon: <Repeat className="h-4 w-4" />,
      color: "orange",
      category: "analysis"
    },
    {
      id: "implications",
      name: "Find Implications",
      description: "Identify theoretical and practical implications of findings.",
      prompt: "Analyze the following findings and identify their theoretical and practical implications for the field:",
      icon: <BarChart3 className="h-4 w-4" />,
      color: "cyan",
      category: "analysis"
    }
  ];

  // Document-Level AI Tools
  const documentAITools = [
    {
      id: "generate-title",
      name: "Generate Title",
      description: "Create compelling academic titles based on content",
      prompt: "Based on the following research content, generate 5 compelling, academic titles that accurately represent the work:",
      icon: <Type className="h-4 w-4" />,
      color: "indigo"
    },
    {
      id: "generate-abstract",
      name: "Generate Abstract",
      description: "Create structured abstract from full document",
      prompt: "Create a structured academic abstract (250 words) from the following research paper content:",
      icon: <FileCheck className="h-4 w-4" />,
      color: "blue"
    },
    {
      id: "extract-keywords",
      name: "Extract Keywords",
      description: "Identify and suggest relevant keywords",
      prompt: "Extract and suggest 8-12 relevant academic keywords from the following research content:",
      icon: <Search className="h-4 w-4" />,
      color: "green"
    },
    {
      id: "generate-outline",
      name: "Generate Outline",
      description: "Create structured outline from existing content",
      prompt: "Create a detailed academic paper outline based on the following content:",
      icon: <List className="h-4 w-4" />,
      color: "purple"
    },
    {
      id: "conclusion-generator",
      name: "Generate Conclusion",
      description: "Create conclusion summarizing key findings and implications",
      prompt: "Based on the following research content, write a comprehensive conclusion that summarizes key findings, implications, and future directions:",
      icon: <Target className="h-4 w-4" />,
      color: "red"
    }
  ];

  // Load document if ID is provided (priority over editor service content)
  useEffect(() => {
    if (initialDocumentId) {
      loadDocument(initialDocumentId);
    } else {
      // Only load from editor service if no document ID is provided
      const editorContent = editorService.getEditorContent();
      if (editorContent && !currentDocument) {
        setTitle(editorContent.title || 'Imported Research Paper');
        setContent(editorContent.content || '');
        updateWordCount(editorContent.content || '');

        // Update editor content explicitly
        if (editorRef.current && editorContent.content) {
          editorRef.current.editor?.commands.setContent(editorContent.content);
        }

        // Clear the editor content after successfully loading it
        editorService.clearEditorContent();
        toast.success('Research paper imported to editor!');
      }
    }
  }, [initialDocumentId]);

  // Listen for content updates from other components (like book generator and Gemini agent)
  useEffect(() => {
    const handleEditorContentUpdate = (event: CustomEvent) => {
      const { title, content } = event.detail;
      console.log('📝 Received editor content update:', { title, contentLength: content?.length });

      if (title && content) {
        setTitle(title);
        setContent(content);
        updateWordCount(content);

        // Update editor content explicitly
        if (editorRef.current) {
          editorRef.current.editor?.commands.setContent(content);
        }

        // Clear the stored content
        editorService.clearEditorContent();
        toast.success(`"${title}" loaded in editor for editing!`);
      }
    };

    const handleGeminiContentUpdate = (event: CustomEvent) => {
      const { content } = event.detail;
      console.log('🤖 Received Gemini content update:', {
        contentLength: content?.length,
        currentContentLength: content?.length,
        hasEditor: !!editorRef.current?.editor
      });

      if (content) {
        console.log('📝 Updating editor content...');

        // Update state
        setContent(content);
        updateWordCount(content);

        // Update editor content explicitly with a small delay to ensure it's ready
        setTimeout(() => {
          if (editorRef.current?.editor) {
            console.log('🔄 Setting editor content...');
            editorRef.current.editor.commands.setContent(content);
            console.log('✅ Editor content updated successfully');
          } else {
            console.warn('⚠️ Editor not available for content update');
          }
        }, 100);

        toast.success('Gemini changes applied to document!');
      } else if (!content) {
        console.warn('⚠️ No content provided in Gemini update event');
      }
    };

    // Add event listeners for custom editor content updates
    window.addEventListener('paperGenius_editorContentUpdated', handleEditorContentUpdate as EventListener);
    window.addEventListener('geminiContentUpdate', handleGeminiContentUpdate as EventListener);

    // Add a global method for direct editor updates
    (window as any).updateEditorContent = (newContent: string) => {
      console.log('🌐 Global updateEditorContent called with content length:', newContent.length);
      setContent(newContent);
      updateWordCount(newContent);

      if (editorRef.current?.editor) {
        editorRef.current.editor.commands.setContent(newContent);
        console.log('✅ Global editor update successful');
      }
    };

    // Cleanup
    return () => {
      window.removeEventListener('paperGenius_editorContentUpdated', handleEditorContentUpdate as EventListener);
      window.removeEventListener('geminiContentUpdate', handleGeminiContentUpdate as EventListener);
      delete (window as any).updateEditorContent;
    };
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (currentDocument && (title !== currentDocument.title || content !== currentDocument.content)) {
      setSaveStatus('unsaved');
      
      const autoSaveTimer = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(autoSaveTimer);
    }
  }, [title, content, currentDocument]);

  // Sync editor content when content state changes
  useEffect(() => {
    if (editorRef.current && content !== editorRef.current.editor?.getHTML()) {
      // Force update the editor content
      editorRef.current.editor?.commands.clearContent();
      setTimeout(() => {
        editorRef.current?.editor?.commands.setContent(content);
      }, 100);
    }
  }, [content]);

  const loadDocument = async (documentId: string) => {
    try {
      const doc = await documentService.getDocument(documentId);
      if (doc) {
        // Clear current state first
        setCurrentDocument(null);
        setTitle('');
        setContent('');

        // Set new document data
        setCurrentDocument(doc);
        setTitle(doc.title);
        setContent(doc.content || '');
        updateWordCount(doc.content || '');
        setSaveStatus('saved');
        setLastSaved(new Date(doc.updated_at));

        // Force editor update
        if (editorRef.current) {
          editorRef.current.editor?.commands.clearContent();
          setTimeout(() => {
            editorRef.current?.editor?.commands.setContent(doc.content || '');
          }, 150);
        }
      }
    } catch (error) {
      console.error('Error loading document:', error);
      toast.error('Failed to load document');
    }
  };

  const handleAutoSave = async () => {
    if (!currentDocument || !user) return;
    
    try {
      setSaveStatus('saving');
      const wordCount = documentService.countWords(content);
      
      await documentService.updateDocument(currentDocument.id, {
        title,
        content,
        word_count: wordCount,
      });
      
      setSaveStatus('saved');
      setLastSaved(new Date());
      updateWordCount(content);
    } catch (error) {
      console.error('Auto-save failed:', error);
      setSaveStatus('error');
    }
  };

  const handleManualSave = async () => {
    if (!user) {
      toast.error('Please log in to save documents');
      return;
    }

    try {
      setSaveStatus('saving');
      const wordCount = documentService.countWords(content);

      if (currentDocument) {
        // Update existing document
        await documentService.updateDocument(currentDocument.id, {
          title,
          content,
          word_count: wordCount,
        });
        toast.success('Document saved successfully');
      } else {
        // Create new document
        const newDoc = await documentService.createDocument({
          title: title || 'Untitled Document',
          content,
          document_type: 'paper',
          status: 'draft',
          metadata: {},
          word_count: wordCount,
        });
        
        if (newDoc) {
          setCurrentDocument(newDoc);
          toast.success('New document created and saved');
        }
      }
      
      setSaveStatus('saved');
      setLastSaved(new Date());
      updateWordCount(content);
    } catch (error) {
      console.error('Save failed:', error);
      setSaveStatus('error');
      toast.error('Failed to save document');
    }
  };

  const handleNewDocument = async () => {
    if (!user) {
      toast.error('Please log in to create documents');
      return;
    }

    try {
      // Create a basic template for new documents
      const defaultContent = `<h1>Research Paper Title</h1>
<p>Start writing your research paper here. Use the AI tools in the sidebar to help with writing, formatting, and content generation.</p>

<h2>Abstract</h2>
<p>Write your abstract here...</p>

<h2>Introduction</h2>
<p>Begin your introduction...</p>

<h2>Literature Review</h2>
<p>Review relevant literature...</p>

<h2>Methodology</h2>
<p>Describe your research methods...</p>

<h2>Results</h2>
<p>Present your findings...</p>

<h2>Discussion</h2>
<p>Discuss your results...</p>

<h2>Conclusion</h2>
<p>Conclude your research...</p>

<h2>References</h2>
<p>List your references...</p>`;

      const newDoc = await documentService.createDocument({
        title: 'Untitled Document',
        content: defaultContent,
        document_type: 'paper',
        status: 'draft',
        metadata: {},
      });

      if (newDoc) {
        // Load the new document using the loadDocument function for consistency
        loadDocument(newDoc.id);
        setActiveView('editor');

        toast.success('New document created');
      }
    } catch (error) {
      console.error('Error creating document:', error);
      toast.error('Failed to create new document');
    }
  };

  const handleDocumentSelect = (document: Document) => {
    // Load the document using the loadDocument function for consistency
    loadDocument(document.id);
    setActiveView('editor');
    setIsDocumentManagerOpen(false);

    // Preserve AI sidebar state - don't change it when opening a document
  };

  const handleImportDocument = () => {
    if (!user) {
      toast.error('Please log in to import documents');
      return;
    }
    
    fileInputRef.current?.click();
  };

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      toast.loading('Importing document...');
      
      const result = await documentImportService.importFile(file);
      
      if (result.success && result.document) {
        // Load the imported document using the loadDocument function for consistency
        loadDocument(result.document.id);
        setActiveView('editor');
        setIsDocumentManagerOpen(false);

        toast.success(`Document "${result.document.title}" imported successfully!`);
      } else {
        toast.error(result.error || 'Failed to import document');
      }
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Failed to import document');
    } finally {
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Convert AI output (markdown-like) to proper HTML
  const convertAIOutputToHTML = (text: string): string => {
    return text
      // Convert headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Convert bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/__(.*?)__/g, '<strong>$1</strong>')
      // Convert italic text
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/_(.*?)_/g, '<em>$1</em>')
      // Convert bullet points
      .replace(/^[\s]*[-*+]\s+(.*$)/gim, '<li>$1</li>')
      // Convert numbered lists
      .replace(/^[\s]*\d+\.\s+(.*$)/gim, '<li>$1</li>')
      // Wrap consecutive list items in ul tags
      .replace(/(<li>.*<\/li>)/gs, (match) => {
        return `<ul>${match}</ul>`;
      })
      // Convert line breaks to paragraphs
      .split('\n\n')
      .map(paragraph => {
        paragraph = paragraph.trim();
        if (!paragraph) return '';
        if (paragraph.startsWith('<h') || paragraph.startsWith('<ul') || paragraph.startsWith('<ol')) {
          return paragraph;
        }
        return `<p>${paragraph.replace(/\n/g, '<br>')}</p>`;
      })
      .join('')
      // Clean up multiple consecutive tags
      .replace(/<\/ul>\s*<ul>/g, '')
      .replace(/<\/ol>\s*<ol>/g, '')
      // Remove empty paragraphs
      .replace(/<p><\/p>/g, '')
      .replace(/<p>\s*<\/p>/g, '');
  };

  // Enhanced AI Functions with better UX
  const handleAIRequest = async (
    prompt: string,
    selectedText: string = '',
    actionType: 'replace' | 'insert' | 'display' = 'display'
  ) => {
    setAiLoading(true);
    setAiLoadingMessage('Initializing AI request...');
    setAiProgress(10);
    setCurrentAIAction(actionType);
    setCurrentPrompt(prompt);

    try {
      setAiLoadingMessage('Processing your request...');
      setAiProgress(30);

      let response: string;
      
      setAiProgress(50);

      // If the prompt is empty, it means we're applying a pre-generated response
      if (!prompt && selectedText) {
        response = selectedText;
      } else {
        if (selectedText) {
          setAiLoadingMessage('Enhancing selected text...');
        } else {
          setAiLoadingMessage('Generating content...');
        }

        const fullPrompt = selectedText ? `${prompt}\n\n"${selectedText}"` : prompt;

        // Use the enhanced AI service with the default model
        response = await enhancedAIService.generateText(fullPrompt);
      }

      setAiProgress(80);
      setAiLoadingMessage('Processing response...');

      // Check if the response indicates an error
      if (response.startsWith('Error:')) {
        toast.error(response);
        setAiLoading(false);
        setAiProgress(0);
        setAiLoadingMessage('');
        return;
      }

      setAiProgress(95);
      setAiLoadingMessage('Finalizing response...');

      // Convert AI output to proper HTML
      const formattedResponse = convertAIOutputToHTML(response);
      setAiResponse(formattedResponse);

      // Position the response popup near the selection or cursor
      if (editorRef.current) {
        const editor = editorRef.current.editor;
        if (editor && selectionRange) {
          const coords = editor.view.coordsAtPos(selectionRange.from);
          const rightMargin = 320; // Width of the popup + padding
          const bottomMargin = 250; // Approximate max height of popup

          setAiResponsePosition({
            x: Math.min(coords.left, window.innerWidth - rightMargin),
            y: Math.min(Math.max(coords.top + 30, 100), window.innerHeight - bottomMargin)
          });
        }
      }

      setAiProgress(100);
      setAiLoadingMessage('Complete!');
      setShowAIResponse(true);
      
      // Auto-apply for quick actions when selected
      if (actionType !== 'display' && selectedText && prompt) {
        await applyAIAction(formattedResponse, actionType, selectedText, prompt);
        setShowAIResponse(false);
        toast.success(`Text ${actionType === 'replace' ? 'replaced' : 'enhanced'} successfully`);
      }
      
    } catch (error: any) {
      console.error('AI request failed:', error);
      
      // Show more informative error messages
      const errorMessage = error.message || 'Unknown error';
      let userMessage = 'AI request failed.';
      
      if (errorMessage.includes('API key')) {
        userMessage = 'API key error: Please check your API configuration.';
      } else if (errorMessage.includes('rate limit')) {
        userMessage = 'Rate limit exceeded. Please try again in a moment.';
      } else if (errorMessage.includes('network')) {
        userMessage = 'Network error. Please check your connection.';
      }
      
      toast.error(userMessage, {
        description: errorMessage.length > 60 ? errorMessage.substring(0, 60) + '...' : errorMessage
      });
      
      // Show a smaller error message in the AI response area
      setAiResponse(`Error: ${userMessage}\n\nPlease try again or check your API configuration.`);
      setShowAIResponse(true);
    } finally {
      setAiLoading(false);
      setAiProgress(0);
      setAiLoadingMessage('');
    }
  };

  // Apply AI action with change tracking integration
  const applyAIAction = async (responseContent: string, actionType: string, originalText?: string, prompt?: string) => {
    if (!editorRef.current) return;

    const editor = editorRef.current.editor;
    if (!editor) return;

    // Record change if tracking is enabled
    let changeId: string | null = null;
    if (changeTracking.isTrackingEnabled && actionType !== 'display') {
      try {
        const { from, to } = editor.state.selection;
        const textToReplace = originalText || editor.state.doc.textBetween(from, to, ' ');

        changeId = changeTracking.recordChange(
          textToReplace,
          responseContent,
          from,
          to,
          actionType as 'replace' | 'insert' | 'display',
          prompt
        );

        console.log('📝 Recorded change in tracking system:', changeId);
      } catch (error) {
        console.error('❌ Failed to record change:', error);
        toast.error('Failed to record change in tracking system');
      }
    }

    switch (actionType) {
      case 'replace':
        if (originalText) {
          // Find and replace the original text
          const { from, to } = editor.state.selection;
          if (from !== to) {
            editor.chain().focus().deleteSelection().insertContent(responseContent).run();
          } else {
            // If no selection, just insert
            editor.chain().focus().insertContent(responseContent).run();
          }
        } else {
          editor.chain().focus().insertContent(responseContent).run();
        }
        if (changeTracking.isTrackingEnabled) {
          toast.success('Text replaced with AI response - tracked for review');
        } else {
          toast.success('Text replaced with AI response');
        }
        break;
      case 'insert':
        editor.chain().focus().insertContent(`<p>${responseContent}</p>`).run();
        if (changeTracking.isTrackingEnabled) {
          toast.success('AI response inserted - tracked for review');
        } else {
          toast.success('AI response inserted into document');
        }
        break;
      default:
        // Just display - do nothing here
        break;
    }
  };

  // Handle document-level AI requests with better UX
  const handleDocumentAIRequest = async (prompt: string, toolId: string) => {
    if (!content.trim()) {
      toast.error('Please add some content to your document first');
      return;
    }
    
    try {
      setAiLoading(true);
      setAiLoadingMessage('Analyzing document...');
      setAiProgress(20);

      setAiProgress(50);
      setAiLoadingMessage('Processing document content...');

      // Use the enhanced AI service for document-level analysis
      const response = await enhancedAIService.generateText(`${prompt}\n\nDocument content:\n"${content}"`);

      setAiProgress(90);
      setAiLoadingMessage('Finalizing analysis...');

      setAiResponse(response);
      setShowAIResponse(true);
      setCurrentAIAction('display');

      setAiProgress(100);
      setAiLoadingMessage('Complete!');

      toast.success('Document analysis completed');
    } catch (error: any) {
      console.error('Document AI request failed:', error);
      toast.error(error.message || 'Failed to analyze document');
    } finally {
      setAiLoading(false);
      setAiProgress(0);
      setAiLoadingMessage('');
    }
  };

  // Handle tool execution from popup panel
  const handleToolExecution = async (toolId: string, mode: 'replace' | 'cursor' | 'popup') => {
    try {
      const context = selectedText.trim() || content.slice(0, 1000);
      const result = await enhancedAIService.executeResearchTool(toolId, context);

      if (mode === 'popup') {
        setAiResponse(result);
        setShowAIResponse(true);
        setCurrentAIAction('display');
      } else {
        await applyAIAction(result, mode === 'replace' ? 'replace' : 'insert');
      }

      toast.success('Tool executed successfully');
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || 'Failed to execute tool');
    }
  };

  // Handle search result insertion
  const handleSearchResultInsert = async (content: string, mode: 'replace' | 'cursor') => {
    try {
      await applyAIAction(content, mode === 'replace' ? 'replace' : 'insert');
      toast.success(`Content ${mode === 'replace' ? 'replaced' : 'inserted'} successfully`);
    } catch (error: any) {
      console.error('Search result insertion error:', error);
      toast.error(error.message || 'Failed to insert content');
    }
  };





  const handleAIChat = async (message: string) => {
    if (!message.trim()) return;
    
    const userMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: message,
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, userMessage]);
    setCurrentInput('');
    setAiLoading(true);
    setAiLoadingMessage('Processing your message...');
    setAiProgress(25);

    try {
      setAiProgress(50);
      setAiLoadingMessage('Generating response...');

      const messages = chatMessages.map(msg => ({
        role: msg.type === 'user' ? 'user' as const : 'assistant' as const,
        content: msg.content
      }));

      messages.push({ role: 'user', content: message });

      setAiProgress(75);
      setAiLoadingMessage('Finalizing response...');

      // Use enhanced AI service for chat
      const response = await enhancedAIService.chatCompletion(messages);

      setAiProgress(100);
      setAiLoadingMessage('Complete!');
      
      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant' as const,
        content: response,
        timestamp: new Date()
      };
      
      setChatMessages(prev => [...prev, assistantMessage]);
      
    } catch (error: any) {
      console.error('Chat request failed:', error);
      
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant' as const,
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      };
      
      setChatMessages(prev => [...prev, errorMessage]);
      toast.error(error.message || 'Chat request failed');
    } finally {
      setAiLoading(false);
      setAiProgress(0);
      setAiLoadingMessage('');
    }
  };

  // Handle sending chat messages
  const handleSendChatMessage = async () => {
    if (!currentInput.trim()) return;
    
    await handleAIChat(currentInput);
    setCurrentInput('');
  };

  const insertAIResponse = (responseContent: string) => {
    if (editorRef.current) {
      const editor = editorRef.current.editor;
      if (editor) {
        editor.chain().focus().insertContent(`<p>${responseContent}</p>`).run();
        toast.success('AI response inserted into document');
      }
    }
  };

  // Export functions
  const handleExportDocument = async (format: 'docx' | 'pdf' | 'html') => {
    if (!editorRef.current || !title) {
      toast.error('No document content to export');
      return;
    }

    try {
      const htmlContent = editorRef.current.getHTML();
      const fileName = title.replace(/[^a-zA-Z0-9\s]/g, '_').replace(/\s+/g, '_');

      switch (format) {
        case 'docx':
          await documentExportService.exportToDocx(title, htmlContent, `${fileName}.docx`);
          toast.success('Document exported as DOCX');
          break;
        case 'pdf':
          await documentExportService.exportToPdf(title, htmlContent, `${fileName}.pdf`);
          toast.success('Document exported as PDF');
          break;
        case 'html':
          const htmlBlob = new Blob([htmlContent], { type: 'text/html' });
          const url = URL.createObjectURL(htmlBlob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${fileName}.html`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          toast.success('Document exported as HTML');
          break;
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error(`Failed to export document as ${format.toUpperCase()}`);
    }
  };

  // Export functions
  const handleTextSelection = () => {
    if (editorRef.current) {
      const editor = editorRef.current.editor;
      if (editor) {
        const { from, to } = editor.state.selection;
        const selectedText = editor.state.doc.textBetween(from, to, ' ');
        setSelectedText(selectedText);
        setSelectionRange({ from, to });

        // Update formatting state based on current selection
        setIsBold(editor.isActive('bold'));
        setIsItalic(editor.isActive('italic'));
        setIsUnderline(editor.isActive('underline'));

        // Update table state
        const inTable = editorRef.current.isInTable();
        setIsInTable(inTable);

        if (inTable) {
          setTableControls({
            canAddColumn: editorRef.current.canAddColumn(),
            canAddRow: editorRef.current.canAddRow(),
            canDeleteColumn: editorRef.current.canDeleteColumn(),
            canDeleteRow: editorRef.current.canDeleteRow(),
            canMergeCells: editorRef.current.canMergeCells(),
            canSplitCell: editorRef.current.canSplitCell()
          });
        }

        // Update image state
        const image = editorRef.current.getSelectedImage();
        setSelectedImage(image);
        setShowImageControls(!!image);

        if (image) {
          setImageWidth(image.attrs?.width || 400);
          setImageAlignmentState(image.attrs?.alignment || null);
        }

        // Show FloatingAIToolbar for text selection
        if (selectedText.trim() && selectedText.length > 3) {
          try {
            // Get position for FloatingAIToolbar
            const editorElement = editor.view.dom;
            const editorBounds = editorElement.getBoundingClientRect();

            // Get the coordinates of the selection
            const selectionCoords = editor.view.coordsAtPos(from);

            const x = selectionCoords ? selectionCoords.left : editorBounds.left + 100;
            const y = selectionCoords ? selectionCoords.top - 10 : editorBounds.top + 100;

            setFloatingAIPosition({ x, y });
            setShowFloatingAI(true);
          } catch (error) {
            console.error("Error getting selection position:", error);
            // Fall back to a default position
            setFloatingAIPosition({ x: window.innerWidth / 2 - 160, y: window.innerHeight / 2 - 100 });
            setShowFloatingAI(true);
          }
        } else if (!isFloatingAIInteracting) {
          // Only hide if user is not interacting with the toolbar
          setShowFloatingAI(false);
        }
      } else {
        setSelectedText('');
        setSelectionRange(null);
        setIsInTable(false);
        if (!isFloatingAIInteracting) {
          setShowFloatingAI(false);
        }
        // Reset formatting state when no editor
        setIsBold(false);
        setIsItalic(false);
        setIsUnderline(false);
      }
    }
  };

  const updateWordCount = (htmlContent: string) => {
    const wordCount = documentService.countWords(htmlContent);
    const charCount = htmlContent.replace(/<[^>]*>/g, '').length;
    setWordCount(wordCount);
    setCharacterCount(charCount);
  };

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    updateWordCount(newContent);

    // Update change tracking with current content
    if (changeTracking.isTrackingEnabled) {
      try {
        changeTracking.updateCurrentContent(newContent);
      } catch (error) {
        console.error('❌ Failed to update change tracking content:', error);
      }
    }
  };

  // Auto-start change tracking when content is loaded
  useEffect(() => {
    if (content && content.trim().length > 0 && !changeTracking.isTrackingEnabled) {
      console.log('🔄 Auto-starting change tracking for document with content length:', content.length);
      changeTracking.startTracking(content);
    }
  }, [content, changeTracking]);

  // Track cursor position for auto-completion
  useEffect(() => {
    if (editorRef.current) {
      const editor = editorRef.current.editor;
      if (editor) {
        const updateCursorPosition = () => {
          const { from } = editor.state.selection;
          setCursorPosition(from);
        };

        // Set up selection change listener
        editor.on('selectionUpdate', updateCursorPosition);

        // Initial cursor position
        updateCursorPosition();

        return () => {
          editor.off('selectionUpdate', updateCursorPosition);
        };
      }
    }
  }, [editorRef.current]);

  // Change tracking handlers
  const handleToggleTracking = (enabled: boolean) => {
    if (enabled) {
      if (!content || content.trim().length === 0) {
        toast.error('Cannot start tracking on empty document. Please add some content first.');
        return;
      }
      console.log('🔄 Starting change tracking manually');
      changeTracking.startTracking(content);
      toast.success('Change tracking started');
    } else {
      console.log('🛑 Stopping change tracking manually');
      changeTracking.stopTracking();
      toast.info('Change tracking stopped');
    }
  };

  const handleToggleDiffView = () => {
    changeTracking.toggleViewMode();
  };

  const handleAcceptChange = (changeId: string) => {
    changeTracking.acceptChange(changeId);
    toast.success('Change accepted');
  };

  const handleRejectChange = (changeId: string) => {
    const originalText = changeTracking.rejectChange(changeId);
    if (originalText && editorRef.current) {
      // Revert the change in the editor
      const currentChange = changeTracking.getCurrentChange();
      if (currentChange) {
        // This would need more sophisticated implementation to properly revert
        // For now, we'll just show a message
        toast.success('Change rejected');
      }
    }
  };

  const handleAcceptAllChanges = () => {
    changeTracking.acceptAllChanges();
    toast.success('All changes accepted');
  };

  const handleRejectAllChanges = () => {
    const originalContent = changeTracking.rejectAllChanges();
    if (originalContent && editorRef.current) {
      setContent(originalContent);
      editorRef.current.editor?.commands.setContent(originalContent);
      toast.success('All changes rejected, content reverted');
    }
  };

  const handleNavigateChange = (direction: 'next' | 'previous') => {
    if (direction === 'next') {
      changeTracking.navigateToNextChange();
    } else {
      changeTracking.navigateToPreviousChange();
    }
  };

  // Section-based change handlers
  const handleAcceptSelectedSections = () => {
    const selectedCount = changeTracking.selectedSections.length;
    changeTracking.acceptSelectedSections();
    toast.success(`Accepted ${selectedCount} selected sections`);
  };

  const handleRejectSelectedSections = () => {
    const selectedCount = changeTracking.selectedSections.length;
    changeTracking.rejectSelectedSections();
    toast.success(`Rejected ${selectedCount} selected sections`);
  };

  // Update formatting state based on editor's current state
  const updateFormattingState = () => {
    if (editorRef.current?.editor) {
      const editor = editorRef.current.editor;
      setIsBold(editor.isActive('bold'));
      setIsItalic(editor.isActive('italic'));
      setIsUnderline(editor.isActive('underline'));
    }
  };

  // Formatting functions that use the editor ref
  const applyFormatting = (format: string, value?: any) => {
    if (editorRef.current) {
      editorRef.current.applyFormat(format, value);
      // Update formatting state after applying format
      setTimeout(updateFormattingState, 10);
    }
  };

  // Handle font family changes - apply to selection or current typing
  const handleFontFamilyChange = (newFontFamily: string) => {
    setFontFamily(newFontFamily);

    if (editorRef.current) {
      let fontValue = '';
      switch (newFontFamily) {
        case 'times-new-roman':
          fontValue = 'Times New Roman, Times, serif';
          break;
        case 'georgia':
          fontValue = 'Georgia, serif';
          break;
        case 'arial':
          fontValue = 'Arial, sans-serif';
          break;
        case 'helvetica':
          fontValue = 'Helvetica, Arial, sans-serif';
          break;
        case 'calibri':
          fontValue = 'Calibri, sans-serif';
          break;
        case 'font-serif':
          fontValue = 'serif';
          break;
        case 'font-sans':
          fontValue = 'sans-serif';
          break;
        case 'font-mono':
          fontValue = 'monospace';
          break;
        default:
          fontValue = 'serif';
      }

      if (fontValue) {
        applyFormatting('font-family', fontValue);
      }
    }
  };

  // Apply font size changes
  useEffect(() => {
    if (editorRef.current && fontSize) {
      const editorElement = editorRef.current.editor?.view.dom;
      if (editorElement) {
        // Remove existing font size classes
        editorElement.classList.remove('text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl');
        // Add new font size class
        editorElement.classList.add(fontSize);
      }
    }
  }, [fontSize]);

  const handleImageUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const src = e.target?.result as string;
          if (editorRef.current) {
            editorRef.current.applyFormat('image', src);
          }
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const handleInsertTable = () => {
    setShowTableDialog(true);
  };

  const confirmInsertTable = () => {
    if (!editorRef.current) {
      toast.error("Editor not available");
      setShowTableDialog(false);
      return;
    }
    if (tableRows < 1 || tableCols < 1) {
      toast.error("Rows and columns must be at least 1");
      return;
    }
    try {
      editorRef.current.applyFormat('table', { rows: tableRows, cols: tableCols });
      toast.success('Table inserted successfully! You can resize columns by dragging the borders.');
    } catch (error) {
      console.error("Error inserting table:", error);
      toast.error("Failed to insert table. Please try again.");
    }
    setShowTableDialog(false);
  };

  // Table manipulation functions
  const handleTableAction = (action: string) => {
    if (editorRef.current) {
      editorRef.current.applyFormat(action);
      // Update table controls after action
      setTimeout(() => handleTextSelection(), 100);
    }
  };

  // Image manipulation functions
  const handleImageAlignment = (alignment: 'left' | 'center' | 'right' | null) => {
    if (editorRef.current && selectedImage) {
      editorRef.current.setImageAlignment(alignment);
      setImageAlignmentState(alignment);
    }
  };

  const handleImageResize = (width: number) => {
    if (editorRef.current && selectedImage && width > 0) {
      editorRef.current.setImageSize(width);
      setImageWidth(width);
    }
  };

  // FloatingAIToolbar handlers
  const handleFloatingAIResult = (result: string, mode: 'replace' | 'insert') => {
    if (editorRef.current && selectionRange) {
      const editor = editorRef.current.editor;
      if (editor) {
        if (mode === 'replace') {
          // Replace selected text
          editor.chain().focus().deleteRange(selectionRange).insertContent(result).run();
        } else {
          // Insert after selection
          editor.chain().focus().setTextSelection(selectionRange.to).insertContent(result).run();
        }

        // Record change in tracking system
        if (changeTracking.isTrackingEnabled) {
          try {
            changeTracking.recordChange(
              selectedText,
              result,
              selectionRange.from,
              selectionRange.to,
              mode,
              'FloatingAI enhancement'
            );
          } catch (error) {
            console.error('Failed to record FloatingAI change:', error);
          }
        }
      }
    }
    // Reset interaction state after applying result
    setIsFloatingAIInteracting(false);
  };

  const handleCloseFloatingAI = () => {
    setShowFloatingAI(false);
    setSelectedText('');
    setSelectionRange(null);
    setIsFloatingAIInteracting(false);
  };

  // Handle floating AI interaction state
  const handleFloatingAIInteractionStart = () => {
    setIsFloatingAIInteracting(true);
  };

  const handleFloatingAIInteractionEnd = () => {
    setIsFloatingAIInteracting(false);
  };

  // Intelligent Writing Assistant handlers
  const handleWritingContentInsert = (content: string, insertPosition: number) => {
    if (editorRef.current) {
      const editor = editorRef.current.editor;
      if (editor) {
        // Insert the content at the specified position
        editor.commands.insertContentAt(insertPosition, content);

        // Move cursor to end of inserted text
        const newPosition = insertPosition + content.length;
        editor.commands.setTextSelection(newPosition);

        // Update cursor position state
        setCursorPosition(newPosition);

        // Update content state
        const newContent = editor.getHTML();
        setContent(newContent);
        updateWordCount(newContent);
      }
    }
  };

  const handleIntelligentWritingToggle = (enabled: boolean) => {
    setIntelligentWritingEnabled(enabled);
    if (enabled) {
      toast.success('AI Writing Assistant enabled');
    } else {
      toast.info('AI Writing Assistant disabled');
    }
  };

  // Update cursor position when editor selection changes
  const handleCursorPositionChange = () => {
    if (editorRef.current) {
      const editor = editorRef.current.editor;
      if (editor) {
        const { from } = editor.state.selection;
        setCursorPosition(from);
      }
    }
  };

  // Handle citation insertion
  const handleCitationInsert = (citation: string, format: string) => {
    if (editorRef.current) {
      const editor = editorRef.current.editor;
      if (editor) {
        // Insert citation at current cursor position
        editor.commands.insertContent(citation);

        // Update content state
        const newContent = editor.getHTML();
        setContent(newContent);
        updateWordCount(newContent);
      }
    }
  };

  const handleImageSizePreset = (preset: 'small' | 'medium' | 'large' | 'full') => {
    let width: number;
    switch (preset) {
      case 'small':
        width = 200;
        break;
      case 'medium':
        width = 400;
        break;
      case 'large':
        width = 600;
        break;
      case 'full':
        width = 800;
        break;
      default:
        width = 400;
    }
    handleImageResize(width);
  };

  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case 'saving':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'saved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getSaveStatusText = () => {
    switch (saveStatus) {
      case 'saving':
        return 'Saving...';
      case 'saved':
        return lastSaved ? `Saved ${formatDistanceToNow(lastSaved, { addSuffix: true })}` : 'Saved';
      case 'error':
        return 'Save failed';
      default:
        return 'Unsaved changes';
    }
  };

  // Function to display current API configuration
  const getAIProviderStatus = () => {
    if (paperAIService.hasValidApiKey()) {
      const provider = paperAIService.getApiProvider();
      return {
        status: 'connected',
        provider,
        label: `Connected to ${provider} API`
      };
    } else {
      return {
        status: 'error',
        provider: 'none',
        label: 'No API Key Configured'
      };
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant={activeView === 'documents' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setActiveView('documents');
                  setIsDocumentManagerOpen(true);
                }}
              >
                <FolderOpen className="h-4 w-4 mr-2" />
                Documents
              </Button>
              <Button
                variant={activeView === 'editor' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveView('editor')}
                disabled={!currentDocument && !content}
              >
                <FileText className="h-4 w-4 mr-2" />
                Editor
              </Button>
            </div>
            
            {activeView === 'editor' && (
              <div className="flex items-center gap-3">
                <Input
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Document title..."
                  className="font-medium text-lg border-none bg-transparent p-0 h-auto focus-visible:ring-0"
                />
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  {getSaveStatusIcon()}
                  <span>{getSaveStatusText()}</span>
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {activeView === 'editor' && (
              <>

                <Button variant="outline" size="sm" onClick={() => setShowDocumentHistory(!showDocumentHistory)}>
                  <History className="h-4 w-4 mr-2" />
                  History
                </Button>
                <Button variant="outline" size="sm" onClick={handleManualSave}>
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button variant="outline" size="sm" onClick={handleImportDocument}>
                  <Import className="h-4 w-4 mr-2" />
                  Import
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                      <ChevronDown className="h-4 w-4 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleExportDocument('docx')}>
                      <FileText className="h-4 w-4 mr-2" />
                      Export as DOCX
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExportDocument('pdf')}>
                      <FileText className="h-4 w-4 mr-2" />
                      Export as PDF
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExportDocument('html')}>
                      <FileText className="h-4 w-4 mr-2" />
                      Export as HTML
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
            
            <Button size="sm" onClick={handleNewDocument}>
              <Plus className="h-4 w-4 mr-2" />
              New
            </Button>
            
            {/* Hidden file input for document import */}
            <input
              ref={fileInputRef}
              type="file"
              accept={documentImportService.getFileAcceptString()}
              onChange={handleFileImport}
              style={{ display: 'none' }}
            />
          </div>
        </div>
      </div>

      {/* Formatting Toolbar Toggle */}
      {activeView === 'editor' && (currentDocument || content) && (
        <div className="flex items-center justify-between bg-white border-b border-gray-100 px-4 py-2">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFormattingToolbar(!showFormattingToolbar)}
              className="toolbar-toggle h-7 px-3 text-gray-600 hover:text-gray-800"
              title={showFormattingToolbar ? "Hide Formatting Toolbar" : "Show Formatting Toolbar"}
            >
              {showFormattingToolbar ? <ChevronUp className="h-4 w-4 mr-1" /> : <ChevronDown className="h-4 w-4 mr-1" />}
              <span className="text-xs font-medium">Formatting</span>
            </Button>
          </div>
          <div className="flex items-center gap-2">

          </div>
        </div>
      )}

      {/* Formatting Toolbar */}
      {activeView === 'editor' && (currentDocument || content) && showFormattingToolbar && (
        <div className={`formatting-toolbar ${showFormattingToolbar ? 'visible toolbar-slide-down' : 'hidden toolbar-slide-up'} bg-white border-b border-gray-200 px-2 sm:px-4 py-2 flex items-center justify-center overflow-x-auto`}>
          <div className="flex items-center space-x-1 sm:space-x-2 max-w-6xl mx-auto flex-wrap gap-1">
          {/* Basic formatting */}
          <Toggle
            variant="outline"
            size="sm"
            pressed={isBold}
            onPressedChange={() => applyFormatting('bold')}
            className="h-8 w-8 sm:h-9 sm:w-9 p-0"
            title="Bold"
          >
            <Bold className="h-3 w-3 sm:h-4 sm:w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            pressed={isItalic}
            onPressedChange={() => applyFormatting('italic')}
            className="h-8 w-8 sm:h-9 sm:w-9 p-0"
            title="Italic"
          >
            <Italic className="h-3 w-3 sm:h-4 sm:w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            pressed={isUnderline}
            onPressedChange={() => applyFormatting('underline')}
            className="h-8 w-8 sm:h-9 sm:w-9 p-0"
            title="Underline"
          >
            <Underline className="h-3 w-3 sm:h-4 sm:w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          {/* Headings */}
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('h1')}
            className="h-8 w-8 p-0"
            title="Heading 1"
          >
            <Type className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('h2')}
            className="h-8 w-8 p-0"
            title="Heading 2"
          >
            <Type className="h-3.5 w-3.5" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('h3')}
            className="h-8 w-8 p-0"
            title="Heading 3"
          >
            <Type className="h-3 w-3" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          {/* Lists */}
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('ul')}
            className="h-8 w-8 p-0"
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('ol')}
            className="h-8 w-8 p-0"
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          {/* Special formatting */}
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('quote')}
            className="h-8 w-8 p-0"
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('code')}
            className="h-8 w-8 p-0"
            title="Code"
          >
            <Code className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('highlight')}
            className="h-8 w-8 p-0"
            title="Highlight"
          >
            <Highlighter className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          {/* Text alignment */}
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-left')}
            className="h-8 w-8 p-0"
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-center')}
            className="h-8 w-8 p-0"
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-right')}
            className="h-8 w-8 p-0"
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-justify')}
            className="h-8 w-8 p-0"
            title="Justify"
          >
            <AlignJustify className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          {/* Insert elements */}
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={handleImageUpload}
            className="h-8 w-8 p-0"
            title="Insert Image"
          >
            <ImageIcon className="h-4 w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            onClick={handleInsertTable}
            className="h-8 w-8 p-0"
            title="Insert Table"
          >
            <Table className="h-4 w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            pressed={showCitationSidebar}
            onClick={() => setShowCitationSidebar(!showCitationSidebar)}
            className="h-8 w-8 p-0"
            title="Citations"
          >
            <Quote className="h-4 w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            pressed={intelligentWritingEnabled}
            onClick={() => handleIntelligentWritingToggle(!intelligentWritingEnabled)}
            className="h-8 w-8 p-0"
            title="AI Writing Assistant"
          >
            <Wand2 className="h-4 w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            onClick={() => applyFormatting('link')}
            className="h-8 w-8 p-0"
            title="Insert Link"
          >
            <Link2 className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          {/* Font controls */}
          <Select value={fontFamily} onValueChange={handleFontFamilyChange}>
            <SelectTrigger className="w-28 sm:w-40 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="font-serif">Serif</SelectItem>
              <SelectItem value="font-sans">Sans Serif</SelectItem>
              <SelectItem value="font-mono">Monospace</SelectItem>
              <SelectItem value="times-new-roman">Times New Roman</SelectItem>
              <SelectItem value="georgia">Georgia</SelectItem>
              <SelectItem value="arial">Arial</SelectItem>
              <SelectItem value="helvetica">Helvetica</SelectItem>
              <SelectItem value="calibri">Calibri</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={fontSize} onValueChange={setFontSize}>
            <SelectTrigger className="w-20 sm:w-28 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text-sm">12px</SelectItem>
              <SelectItem value="text-base">14px</SelectItem>
              <SelectItem value="text-lg">16px</SelectItem>
              <SelectItem value="text-xl">18px</SelectItem>
              <SelectItem value="text-2xl">20px</SelectItem>
            </SelectContent>
          </Select>
          </div>
        </div>
      )}

      {/* Table Manipulation Toolbar - appears when cursor is in a table */}
      {activeView === 'editor' && isInTable && (currentDocument || content) && (
        <div className="bg-blue-50 border-b border-blue-200 px-2 py-1.5 flex items-center space-x-1 overflow-x-auto">
          <div className="flex items-center space-x-1 text-sm text-blue-700 font-medium mr-4">
            <Table className="h-4 w-4" />
            <span>Table Tools:</span>
          </div>

          {/* Column operations */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('addColumnBefore')}
            disabled={!tableControls.canAddColumn}
            className="h-7 px-2 text-xs"
            title="Add Column Before"
          >
            ← Col
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('addColumnAfter')}
            disabled={!tableControls.canAddColumn}
            className="h-7 px-2 text-xs"
            title="Add Column After"
          >
            Col →
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('deleteColumn')}
            disabled={!tableControls.canDeleteColumn}
            className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
            title="Delete Column"
          >
            Del Col
          </Button>

          <div className="h-4 w-px bg-blue-300 mx-1" />

          {/* Row operations */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('addRowBefore')}
            disabled={!tableControls.canAddRow}
            className="h-7 px-2 text-xs"
            title="Add Row Before"
          >
            ↑ Row
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('addRowAfter')}
            disabled={!tableControls.canAddRow}
            className="h-7 px-2 text-xs"
            title="Add Row After"
          >
            Row ↓
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('deleteRow')}
            disabled={!tableControls.canDeleteRow}
            className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
            title="Delete Row"
          >
            Del Row
          </Button>

          <div className="h-4 w-px bg-blue-300 mx-1" />

          {/* Cell operations */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('mergeCells')}
            disabled={!tableControls.canMergeCells}
            className="h-7 px-2 text-xs"
            title="Merge Cells"
          >
            Merge
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('splitCell')}
            disabled={!tableControls.canSplitCell}
            className="h-7 px-2 text-xs"
            title="Split Cell"
          >
            Split
          </Button>

          <div className="h-4 w-px bg-blue-300 mx-1" />

          {/* Table operations */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('toggleHeaderRow')}
            className="h-7 px-2 text-xs"
            title="Toggle Header Row"
          >
            Header Row
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableAction('deleteTable')}
            className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
            title="Delete Table"
          >
            Del Table
          </Button>
        </div>
      )}

      {/* Image Controls Toolbar - appears when an image is selected */}
      {activeView === 'editor' && showImageControls && selectedImage && (currentDocument || content) && (
        <div className="bg-green-50 border-b border-green-200 px-2 py-1.5 flex items-center space-x-1 overflow-x-auto">
          <div className="flex items-center space-x-1 text-sm text-green-700 font-medium mr-4">
            <ImageIcon className="h-4 w-4" />
            <span>Image Tools:</span>
          </div>

          {/* Alignment controls */}
          <div className="flex items-center space-x-1">
            <span className="text-xs text-green-600 mr-2">Align:</span>
            <Button
              variant={imageAlignment === 'left' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleImageAlignment('left')}
              className="h-7 px-2 text-xs"
              title="Align Left"
            >
              <AlignLeft className="h-3 w-3" />
            </Button>
            <Button
              variant={imageAlignment === 'center' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleImageAlignment('center')}
              className="h-7 px-2 text-xs"
              title="Align Center"
            >
              <AlignCenter className="h-3 w-3" />
            </Button>
            <Button
              variant={imageAlignment === 'right' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleImageAlignment('right')}
              className="h-7 px-2 text-xs"
              title="Align Right"
            >
              <AlignRight className="h-3 w-3" />
            </Button>
            <Button
              variant={imageAlignment === null ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleImageAlignment(null)}
              className="h-7 px-2 text-xs"
              title="No Alignment"
            >
              None
            </Button>
          </div>

          <div className="h-4 w-px bg-green-300 mx-1" />

          {/* Size presets */}
          <div className="flex items-center space-x-1">
            <span className="text-xs text-green-600 mr-2">Size:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleImageSizePreset('small')}
              className="h-7 px-2 text-xs"
              title="Small (200px)"
            >
              S
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleImageSizePreset('medium')}
              className="h-7 px-2 text-xs"
              title="Medium (400px)"
            >
              M
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleImageSizePreset('large')}
              className="h-7 px-2 text-xs"
              title="Large (600px)"
            >
              L
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleImageSizePreset('full')}
              className="h-7 px-2 text-xs"
              title="Full Width (800px)"
            >
              XL
            </Button>
          </div>

          <div className="h-4 w-px bg-green-300 mx-1" />

          {/* Custom width input */}
          <div className="flex items-center space-x-1">
            <span className="text-xs text-green-600">Width:</span>
            <Input
              type="number"
              min="50"
              max="1200"
              value={imageWidth}
              onChange={(e) => {
                const width = parseInt(e.target.value) || 400;
                setImageWidth(width);
              }}
              onBlur={() => handleImageResize(imageWidth)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleImageResize(imageWidth);
                }
              }}
              className="w-16 h-7 text-xs"
              title="Custom width in pixels"
            />
            <span className="text-xs text-green-600">px</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Documents View */}
        {activeView === 'documents' && (
          <div className="flex-1 overflow-y-auto">
            <DocumentManager
              onOpenDocument={handleDocumentSelect}
              onNewDocument={handleNewDocument}
              onImportDocument={handleImportDocument}
              selectedDocumentId={currentDocument?.id}
            />
          </div>
        )}

        {/* Editor View - independent of sidebar state */}
        {activeView === 'editor' && (
          <div className="flex-1 flex overflow-hidden editor-main-content">
            {/* Main Editor Content */}
            {currentDocument || content ? (
              <div className="flex-1 flex flex-col overflow-hidden editor-container">
                {/* Editor content with consistent spacing */}
                <div className="overflow-y-auto flex-1">
                  <div className="editor-content-area max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-4">
                      {/* Editor Title */}
                      <Input
                        className="text-2xl sm:text-3xl font-bold border-none bg-transparent px-0 focus-visible:ring-0 focus-visible:outline-none focus-visible:shadow-none"
                        placeholder="Untitled Document"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        onBlur={handleManualSave}
                      />
                    </div>

                    {/* Change Tracking Controls */}
                    <div className="mb-4">
                      <ChangeTrackingControls
                        onToggleTracking={handleToggleTracking}
                        onToggleDiffView={handleToggleDiffView}
                        onAcceptAllChanges={handleAcceptAllChanges}
                        onRejectAllChanges={handleRejectAllChanges}
                      />
                    </div>

                    {/* Section-Based Diff Viewer - shown when in diff mode */}
                    {changeTracking.viewMode === 'diff' && changeTracking.hasChanges && (
                      <div className="mb-4">
                        <SectionBasedDiffViewer
                          onAcceptSelectedSections={handleAcceptSelectedSections}
                          onRejectSelectedSections={handleRejectSelectedSections}
                          onAcceptAllChanges={handleAcceptAllChanges}
                          onRejectAllChanges={handleRejectAllChanges}
                          onNavigateChange={handleNavigateChange}
                        />
                      </div>
                    )}

                    {/* Editor Area - completely independent of sidebar state */}
                    <div className="prose prose-sm sm:prose lg:prose-lg xl:prose-xl max-w-none bg-white rounded-lg shadow-sm border border-gray-200 relative z-0">
                      <div className="px-8 py-6">
                        <RichTextEditor
                          ref={editorRef}
                          content={content}
                          onChange={handleContentChange}
                          onSelectionChange={handleTextSelection}
                          className="w-full editor-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center bg-white">
                <div className="text-center max-w-md">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No document open
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Create a new document, import an existing one, or open from your document library.
                  </p>
                  <div className="flex gap-3 justify-center">
                    <Button onClick={handleNewDocument}>
                      <Plus className="h-4 w-4 mr-2" />
                      New Document
                    </Button>
                    <Button variant="outline" onClick={handleImportDocument}>
                      <Import className="h-4 w-4 mr-2" />
                      Import Document
                    </Button>
                    <Button variant="outline" onClick={() => setActiveView('documents')}>
                      <FolderOpen className="h-4 w-4 mr-2" />
                      Browse Documents
                    </Button>
                  </div>
                  <div className="mt-4 text-sm text-gray-500">
                    Supported formats: {documentImportService.getSupportedFileTypes().join(', ')}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      


      {/* AI Loading Modal */}
      {aiLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="mb-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Processing</h3>
                <p className="text-sm text-gray-600 mb-4">{aiLoadingMessage || 'Processing your request...'}</p>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${aiProgress}%` }}
                ></div>
              </div>

              {/* Progress Percentage */}
              <div className="text-xs text-gray-500">
                {aiProgress}% Complete
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced AI Response Popup for generated content */}
      {showAIResponse && aiResponse && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 backdrop-blur-sm"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowAIResponse(false);
            }
          }}
        >
          <div className="bg-white rounded-xl shadow-2xl max-w-5xl max-h-[85vh] w-full mx-4 flex flex-col border border-gray-200">
            {/* Enhanced Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Sparkles className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">AI Generated Content</h3>
                  <p className="text-sm text-gray-600 mt-1">Review and choose how to use this content</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAIResponse(false)}
                className="text-gray-500 hover:text-gray-700 hover:bg-white/50 rounded-lg"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Enhanced Content Area */}
            <div className="flex-1 overflow-auto p-6">
              <div className="prose prose-lg max-w-none">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200 shadow-inner">
                  <div
                    className="whitespace-pre-wrap text-gray-800 leading-relaxed font-medium"
                    style={{
                      fontFamily: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',
                      fontSize: '16px',
                      lineHeight: '1.7'
                    }}
                  >
                    {aiResponse}
                  </div>
                </div>
              </div>

              {/* Content Statistics */}
              <div className="mt-4 flex items-center gap-4 text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
                <span className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  {aiResponse.split(' ').length} words
                </span>
                <span className="flex items-center gap-1">
                  <Type className="h-3 w-3" />
                  {aiResponse.length} characters
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Generated just now
                </span>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6">
              <div className="flex flex-col gap-4">
                {/* Primary Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Button
                      onClick={() => {
                        navigator.clipboard.writeText(aiResponse);
                        toast.success('Content copied to clipboard');
                      }}
                      variant="outline"
                      size="default"
                      className="flex items-center gap-2 hover:bg-white border-gray-300"
                    >
                      <Copy className="h-4 w-4" />
                      Copy to Clipboard
                    </Button>

                    <Button
                      onClick={() => {
                        insertAIResponse(aiResponse);
                        setShowAIResponse(false);
                        toast.success('Content inserted at cursor position');
                      }}
                      variant="default"
                      size="default"
                      className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="h-4 w-4" />
                      Insert at Cursor
                    </Button>

                    {currentAIAction !== 'display' && selectedText && (
                      <Button
                        onClick={() => {
                          applyAIAction(aiResponse, currentAIAction, selectedText, currentPrompt);
                          setShowAIResponse(false);
                          toast.success(`Text ${currentAIAction === 'replace' ? 'replaced' : 'inserted'} successfully`);
                        }}
                        variant="default"
                        size="default"
                        className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                      >
                        <Edit className="h-4 w-4" />
                        {currentAIAction === 'replace' ? 'Replace Selected Text' : 'Insert After Selection'}
                      </Button>
                    )}
                  </div>

                  <Button
                    onClick={() => setShowAIResponse(false)}
                    variant="ghost"
                    size="default"
                    className="text-gray-600 hover:text-gray-800"
                  >
                    Keep for Later
                  </Button>
                </div>

                {/* Secondary Actions */}
                <div className="flex items-center gap-2 pt-2 border-t border-gray-200">
                  <span className="text-sm text-gray-600 font-medium">Quick Actions:</span>
                  <Button
                    onClick={() => {
                      // Regenerate with same prompt
                      if (currentPrompt) {
                        handleAIRequest(currentPrompt, selectedText, currentAIAction);
                        setShowAIResponse(false);
                      }
                    }}
                    variant="ghost"
                    size="sm"
                    className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                    disabled={!currentPrompt}
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Regenerate
                  </Button>
                  <Button
                    onClick={() => {
                      // Request improvement
                      if (aiResponse) {
                        const improvePrompt = `Please improve and refine the following text for better clarity, flow, and academic quality:\n\n${aiResponse}`;
                        handleAIRequest(improvePrompt, '', 'display');
                        setShowAIResponse(false);
                      }
                    }}
                    variant="ghost"
                    size="sm"
                    className="text-xs text-purple-600 hover:text-purple-800 hover:bg-purple-50"
                  >
                    <Sparkles className="h-3 w-3 mr-1" />
                    Improve
                  </Button>
                  <Button
                    onClick={() => {
                      // Request shorter version
                      if (aiResponse) {
                        const shortenPrompt = `Please create a more concise version of the following text while preserving key information:\n\n${aiResponse}`;
                        handleAIRequest(shortenPrompt, '', 'display');
                        setShowAIResponse(false);
                      }
                    }}
                    variant="ghost"
                    size="sm"
                    className="text-xs text-orange-600 hover:text-orange-800 hover:bg-orange-50"
                  >
                    <Minimize2 className="h-3 w-3 mr-1" />
                    Shorten
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Table Insert Dialog */}
      {showTableDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Insert Table</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Rows
                </label>
                <Input
                  type="number"
                  min="1"
                  max="50"
                  value={tableRows}
                  onChange={(e) => setTableRows(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Columns
                </label>
                <Input
                  type="number"
                  min="1"
                  max="20"
                  value={tableCols}
                  onChange={(e) => setTableCols(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-full"
                />
              </div>
              <div className="text-sm text-gray-500">
                You can add more rows and columns later using the table tools.
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowTableDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={confirmInsertTable}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Insert Table
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Floating AI Toolbar for text selection */}
      {showFloatingAI && selectedText && (
        <FloatingAIToolbar
          selectedText={selectedText}
          position={floatingAIPosition}
          onApplyResult={handleFloatingAIResult}
          onClose={handleCloseFloatingAI}
          isLoading={aiLoading}
          documentContext={content.slice(0, 1000)} // Provide document context
          onInteractionStart={handleFloatingAIInteractionStart}
          onInteractionEnd={handleFloatingAIInteractionEnd}
          onSendToAssistant={(result, toolName) => {
            // Route the result to the main AI assistant
            setAiResponse(result);
            setShowUnifiedAI(true);
            toast.success(`${toolName} result sent to AI Assistant`);
          }}
        />
      )}

      {/* Unified AI Assistant */}
      <UnifiedAIAssistant
        onAIRequest={handleAIRequest}
        selectedText={selectedText}
        documentContent={content}
        aiLoading={aiLoading}
        aiResponse={aiResponse}
        isVisible={showUnifiedAI}
        onToggleVisibility={() => setShowUnifiedAI(!showUnifiedAI)}
      />

      {/* Intelligent Writing Assistant */}
      <IntelligentWritingAssistant
        content={content}
        cursorPosition={cursorPosition}
        onContentInsert={handleWritingContentInsert}
        isEnabled={intelligentWritingEnabled}
        onToggleEnabled={handleIntelligentWritingToggle}
      />

      {/* Academic Citation Sidebar */}
      <AcademicCitationSidebar
        isVisible={showCitationSidebar}
        onToggleVisibility={() => setShowCitationSidebar(!showCitationSidebar)}
        onCitationInsert={handleCitationInsert}
        documentContent={content}
      />
    </div>
  );
}
