@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-size: 16px; /* Ensure consistent base font size */
    zoom: 1; /* Reset any browser zoom */
    -webkit-text-size-adjust: 100%; /* Prevent iOS text size adjustment */
    -ms-text-size-adjust: 100%; /* Prevent Windows Phone text size adjustment */
  }

  html, body {
    width: 100%;
    min-height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    overflow-y: auto;
    transform: scale(1); /* Ensure no scaling */
    transform-origin: 0 0;
  }

  body {
    @apply bg-background text-foreground;
    font-size: 1rem; /* Consistent body font size */
    line-height: 1.5;
  }

  /* Ensure root container takes full viewport */
  #root {
    width: 100vw;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
  }

  /* Fix any potential zoom issues in containers */
  .min-h-screen {
    min-height: 100vh !important;
  }

  /* Ensure proper scaling for all containers */
  * {
    box-sizing: border-box;
  }
}

/* Enhanced Citation Link Styles */
@layer components {
  .citation-link {
    @apply inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 underline decoration-dotted hover:decoration-solid transition-colors duration-200;
    position: relative;
  }

  .citation-link svg {
    @apply w-3 h-3 opacity-70;
  }

  .citation-link:hover svg {
    @apply opacity-100;
  }

  .citation-link:hover {
    @apply shadow-sm;
  }

  /* Animation for citation links */
  .citation-link {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-2px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced section display animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* AI Autocompletion Animations */
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: translateY(-100%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(-100%) scale(1);
    }
  }

  @keyframes animate-accept {
    from {
      opacity: 1;
      transform: translateY(-100%) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-100%) scale(1.05) translateX(20px);
    }
  }

  @keyframes animate-reject {
    from {
      opacity: 1;
      transform: translateY(-100%) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-100%) scale(0.95) translateX(-20px);
    }
  }

  .animate-in {
    animation: animate-in 0.3s ease-out;
  }

  .animate-accept {
    animation: animate-accept 0.2s ease-in;
  }

  .animate-reject {
    animation: animate-reject 0.2s ease-in;
  }

  /* Suggestion preview styles */
  .ai-suggestion-preview {
    background-color: rgba(59, 130, 246, 0.1);
    border-left: 2px solid #3b82f6;
    padding: 0.5rem;
    margin: 0.25rem 0;
    border-radius: 0.25rem;
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }

  .ai-suggestion-preview:hover {
    opacity: 1;
  }

  /* Citation highlight styles */
  .citation-highlight {
    background-color: rgba(34, 197, 94, 0.1);
    border-bottom: 1px dotted #22c55e;
    cursor: pointer;
  }

  .citation-highlight:hover {
    background-color: rgba(34, 197, 94, 0.2);
  }

  /* Enhanced editor specific styles */
  .enhanced-editor-content {
    position: relative;
  }

  .enhanced-editor-content .ProseMirror {
    outline: none;
    padding: 1rem;
    min-height: 400px;
  }

  .enhanced-editor-content .ProseMirror p.is-editor-empty:first-child::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
}